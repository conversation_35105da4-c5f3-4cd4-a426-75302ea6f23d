import { z as zod } from 'zod';

import { PRODUCT_TYPES } from 'src/actions/mooly-chatbot/product-constants';

import { schemaHelper } from 'src/components/hook-form';

// ----------------------------------------------------------------------

// Base schema chung cho tất cả loại sản phẩm
const createBaseProductSchema = () => zod.object({
  // Thông tin cơ bản - bắt buộc cho tất cả loại
  name: zod.string().min(1, 'Tên sản phẩm là bắt buộc'),
  description: zod.string().optional().or(zod.literal('')),
  url: zod.string().optional().or(zod.literal('')),

  // Phân loại - bắt buộc
  categoryId: zod.string().min(1, 'Danh mục là bắt buộc'),
  type: zod.enum([
    PRODUCT_TYPES.SIMPLE,
    PRODUCT_TYPES.VARIABLE,
    PRODUCT_TYPES.DIGITAL,
    PRODUCT_TYPES.SERVICE,
  ]).default(PRODUCT_TYPES.SIMPLE),

  // Hình ảnh - bắt buộc cho tất cả loại
  images: zod.array(zod.any()).min(1, 'Ít nhất một hình ảnh sản phẩm là bắt buộc'),
  avatar: zod.any().optional(),

  // Trạng thái
  isActive: zod.boolean().default(true),

  // Các field tự động tạo hoặc có giá trị mặc định
  slug: zod.string().optional().or(zod.literal('')),
  sku: zod.string().optional().or(zod.literal('')), // Sẽ tự động tạo từ tên sản phẩm
  stockQuantity: zod.number().default(0),
  trackInventory: zod.boolean().default(false),

  // Các field tùy chọn - sẽ được override trong schema cụ thể
  costPrice: zod.number().min(0).optional().nullable(),
  salePrice: zod.number().min(0).optional().nullable(),
  weight: zod.number().min(0).optional().nullable(),
  length: zod.number().min(0).optional().nullable(),
  width: zod.number().min(0).optional().nullable(),
  height: zod.number().min(0).optional().nullable(),
  attributes: zod.any().default([]),
  variants: zod.any().default([]),
  tags: zod.array(zod.string()).default([]),
  gender: zod.array(zod.string()).default([]),
  saleLabel: zod.any().default({ enabled: false, content: '' }),
  newLabel: zod.any().default({ enabled: false, content: '' }),
  taxes: zod.number().min(0).optional().nullable(),
  includeTaxes: zod.boolean().default(false),
  metaKeywords: zod.array(zod.string()).default([]),
  seoTitle: zod.string().optional().or(zod.literal('')),
  seoDescription: zod.string().optional().or(zod.literal('')),
  isFeatured: zod.boolean().default(false),
  marketingInfo: zod.any().default({}),
  inventorySettings: zod.any().default({}),
  pricingSettings: zod.any().default({}),
  digitalProductInfo: zod.any().default({}),
  serviceInfo: zod.any().default({}),
  bundleInfo: zod.any().default({}),
  dimensions: zod.any().default({}),
});

// Schema cho sản phẩm đơn giản - chỉ cần giá cơ bản
const createSimpleProductSchema = () => createBaseProductSchema().extend({
  // Giá - bắt buộc cho sản phẩm simple
  price: zod.number({
    required_error: 'Giá sản phẩm là bắt buộc',
    invalid_type_error: 'Giá phải là số'
  }).min(0, 'Giá phải lớn hơn hoặc bằng 0'),

  // Override các field không cần thiết cho simple product
  attributes: zod.array(zod.any()).default([]),
  variants: zod.array(zod.any()).default([]),
});

// Schema cho sản phẩm có biến thể
const createVariableProductSchema = () => createBaseProductSchema().extend({
  // Giá cơ bản - bắt buộc cho variable product
  price: zod.number({
    required_error: 'Giá cơ bản là bắt buộc',
    invalid_type_error: 'Giá phải là số'
  }).min(0, 'Giá phải lớn hơn hoặc bằng 0'),

  // Thuộc tính - bắt buộc cho variable product
  attributes: zod.array(zod.object({
    name: zod.string().min(1, 'Tên thuộc tính là bắt buộc'),
    values: zod.array(zod.string()).min(1, 'Ít nhất một giá trị thuộc tính là bắt buộc'),
  })).min(1, 'Ít nhất một thuộc tính là bắt buộc cho sản phẩm có biến thể'),

  // Biến thể - sẽ được validate sau khi tạo attributes
  variants: zod.array(zod.object({
    id: zod.string().optional(),
    sku: zod.string().optional(),
    price: zod.number().min(0, 'Giá biến thể phải lớn hơn hoặc bằng 0'),
    stockQuantity: zod.number().min(0, 'Số lượng tồn kho phải lớn hơn hoặc bằng 0').default(0),
    isActive: zod.boolean().default(true),
    attributes: zod.record(zod.string()).optional(),
    image: zod.any().optional(),
  })).default([]),
});

// ----------------------------------------------------------------------

// Function để tạo schema dựa trên loại sản phẩm - QUAN TRỌNG cho validation đồng bộ
export const getSchemaByType = (productType) => {
  switch (productType) {
    case PRODUCT_TYPES.VARIABLE:
      return createVariableProductSchema();
    case PRODUCT_TYPES.SIMPLE:
    default:
      return createSimpleProductSchema();
  }
};

// Schema chính - sử dụng schema đơn giản làm mặc định
export const ProductSchema = createSimpleProductSchema();

// Schema đơn giản cho sản phẩm simple
export const SimpleProductSchema = createSimpleProductSchema();

// Schema cho sản phẩm variable
export const VariableProductSchema = createVariableProductSchema();

// ----------------------------------------------------------------------

// Default values chung cho tất cả loại sản phẩm
const baseDefaultValues = {
  // Thông tin cơ bản
  name: '',
  description: '',
  url: '',

  // Phân loại
  categoryId: '',
  type: PRODUCT_TYPES.SIMPLE,

  // Hình ảnh và media
  images: [],
  avatar: null,

  // Trạng thái
  isActive: true,

  // Các field tự động tạo
  slug: '',
  sku: '',
  stockQuantity: 0,
  trackInventory: false,

  // Các field tùy chọn
  costPrice: null,
  salePrice: null,
  weight: null,
  length: null,
  width: null,
  height: null,
  tags: [],
  gender: [],
  saleLabel: { enabled: false, content: '' },
  newLabel: { enabled: false, content: '' },
  taxes: null,
  includeTaxes: false,
  metaKeywords: [],
  seoTitle: '',
  seoDescription: '',
  isFeatured: false,
  marketingInfo: {},
  inventorySettings: {},
  pricingSettings: {},
  digitalProductInfo: {},
  serviceInfo: {},
  bundleInfo: {},
  dimensions: {},
};

// Default values cho sản phẩm đơn giản
export const simpleProductDefaultValues = {
  ...baseDefaultValues,
  // Giá - bắt buộc cho simple
  price: null,
  // Không cần attributes và variants
  attributes: [],
  variants: [],
};

// Default values cho sản phẩm có biến thể
export const variableProductDefaultValues = {
  ...baseDefaultValues,
  // Giá cơ bản - bắt buộc cho variable
  price: null,
  // Cần attributes và variants
  attributes: [],
  variants: [],
};

// Function để lấy default values theo loại sản phẩm
export const getDefaultValuesByType = (productType) => {
  switch (productType) {
    case PRODUCT_TYPES.VARIABLE:
      return variableProductDefaultValues;
    case PRODUCT_TYPES.SIMPLE:
    default:
      return simpleProductDefaultValues;
  }
};

// Default values chính - sử dụng simple làm mặc định
export const defaultValues = simpleProductDefaultValues;
